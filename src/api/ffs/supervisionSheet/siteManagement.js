import request from '@/utils/request'
import {basicPath4, basicPath1, basicPath2} from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
import {xmbBaseInfo} from "@/api/xmb/xmbCommon";

// 查询牧场列表
export function pasturelist(params) {
    return request({
        url: basicPath4 + 'pasture/list',
        method: 'post',
        data: params
    })
}
//查询牧场下的活畜

export function pastureLivList(params) {
    return request({
        url: basicPath4 + 'pastureLivestock/list',
        method: 'post',
        data: params
    })
}
//牧场新增

export function pastureAdd(params) {
    return request({
        url: basicPath4 + 'pasture/add',
        method: 'post',
        data: params
    })
}
//牧场编辑
export function pastureEdit(params) {
    return request({
        url: basicPath4 + 'pasture/edit',
        method: 'post',
        data: params
    })
}
//查询牧场详情
export function pastureById(params) {
    return request({
        url: basicPath4 + 'pasture/selectById',
        method: 'post',
        data: params
    })
}
// 单只绑定耳标
export function earTagAdd(params) {
    return request({
        url: basicPath4 + 'pastureLivestock/insert',
        method: 'post',
        data: params
    })
}
// 批量
export function earTagBatch(params) {
    return request({
        url: basicPath4 + 'pastureLivestock/batchInsert',
        method: 'post',
        data: params
    })
}
//视频监控列表
export function pastureVideoList(params) {
    return request({
        url: basicPath4 + 'pasture/selectPastureVideo',
        method: 'post',
        data: params
    })
}
//牧场视频列表
export function selectPastureVideoForFfs(params) {
    return request({
        url: basicPath4 + 'pasture/selectPastureVideoForFfs',
        method: 'post',
        data: params
    })
}
//添加监控设备
export function addVideo(params) {
    return request({
        url: basicPath4 + 'pasture/addPastureVideo',
        method: 'post',
        data: params
    })
}



//获取地址播放视频
export function getPlayUrl(params) {
    return request({
        url: basicPath4 + 'pasture/getPlayUrl',
        method: 'post',
        data: params
    })
}
// 获取视频播放地址
export function getVideoUrl(params) {
    return request({
        url: basicPath4 + 'pasture/getLiveVideoUrl',
        method: 'post',
        data: params
    })
}

//删除牧场视频
export function deleteVideo(params) {
  return request({
    url: basicPath4 + 'pasture/deleteVideoById',
    method: 'post',
    data: params
  })
}

//获取萤石云的播放地址
export function ezvizUrl(params) {
    return request({
        url: basicPath4 + 'ezviz/getPlayUrl',
        method: 'post',
        data: params
    })
}
//视频编辑接口
export function editVideo(params) {
    return request({
        url: basicPath4 + 'pasture/updateById',
        method: 'post',
        data: params
    })
}
// 活畜列表
export function livestockList(data) {
  return request({
    url: `${basicPath2}livestock/livestock/list`,
    method: 'post',
    data: data
  })
}

// 活畜列表
export function animalTypeList(data) {
  return request({
    url: `${basicPath2}livestock/livestockCategory/list`,
    method: 'post',
    data: data
  })
}

// 活畜列表
export function varietiesList(data) {
  return request({
    url: `${basicPath2}livestock/livestockVarieties/list`,
    method: 'post',
    data: data
  })
}

// 耳标编辑
export function earTagDelete(data) {
    return request({
        url: `${basicPath4}pastureLivestock/deleteById`,
        method: 'post',
        data: data
    })
}

//活畜编辑
export function earTagEdit(data) {
    return request({
        url: `${basicPath4}pastureLivestock/edit`,
        method: 'post',
        data: data
    })
}

//耳标导出
export function exportList(data) {
    return request({
        url: `${basicPath4}pastureLivestock/exportLivestockList`,
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}
//批量上传文件
export function batchCompleteLivestock(data) {
    return request({
        url: `${basicPath4}pastureLivestock/batchCompleteLivestock`,
        method: 'post',
        data: data,
    })
}
//耳标解绑
export function unboundLivestock(params) {
    return request({
        url: basicPath4 + 'pastureLivestock/unboundLivestock',
        method: 'post',
        data: params
    })
}
//耳标批量解绑
export function batchUnboundLivestock(params) {
    return request({
        url: basicPath4 + 'pastureLivestock/batchUnboundLivestock',
        method: 'post',
        data: params
    })
}
//离线视频新增
export function offlineVideoAdd(params) {
    return request({
        url: basicPath4 + 'offlineVideo/add',
        method: 'post',
        data: params
    })
}

//离线视频列表
export function offlineVideoList(params) {
    return request({
        url: basicPath4 + 'offlineVideo/list',
        method: 'post',
        data: params
    })
}
//离线视频编辑
export function offlineVideoEdit(params) {
    return request({
        url: basicPath4 + 'offlineVideo/edit',
        method: 'post',
        data: params
    })
}
//离线视频删除
export function offlineVideoDelete(params) {
    return request({
        url: basicPath4 + 'offlineVideo/deleteVideoById',
        method: 'post',
        data: params
    })
}
//离线视频删除
export function selectPastureVideoByApplyId(params) {
    return request({
        url: basicPath4 + 'pasture/selectPastureVideoByApplyId',
        method: 'post',
        data: params
    })
}
//活畜信息导入

export function batchModel(data) {
    return process.env.VUE_APP_BASE_API + `${basicPath4}pastureLivestock/importLivestockModel`

}

// 查询牧场下圈舍列表
export function selectPasturePenList(params) {
  return request({
    url: basicPath4 + 'pastureLivestock/selectPasturePenList',
    method: 'post',
    data: params
  })
}

// 新增圈舍
export function addPasturePen(params) {
  return request({
    url: basicPath4 + 'pastureLivestock/addPasturePen',
    method: 'post',
    data: params
  })
}

// 编辑圈舍
export function editPasturePen(params) {
  return request({
    url: basicPath4 + 'pastureLivestock/editPasturePen',
    method: 'post',
    data: params
  })
}

// 圈舍详情
export function selectPasturePenInfo(params) {
  return request({
    url: basicPath4 + 'pastureLivestock/selectPasturePenInfo',
    method: 'post',
    data: params
  })
}

// 删除圈舍
export function deletePasturePen(params) {
  return request({
    url: basicPath4 + 'pastureLivestock/deletePasturePen',
    method: 'post',
    data: params
  })
}


// 新增栏位 pasture/pen/fence/add
export function fenceAdd(params) {
  return request({
    url: basicPath2 + 'pasture/pen/fence/add',
    method: 'post',
    data: params
  })
}

// 编辑栏位 pasture/pen/fence/edit
export function fenceEdit(params) {
  return request({
    url: basicPath2 + 'pasture/pen/fence/edit',
    method: 'post',
    data: params
  })
}


// 栏位列表 pasture/pen/fence/list
export function fenceList(params) {
  return request({
    url: basicPath2 + 'pasture/pen/fence/list',
    method: 'post',
    data: params
  })
}