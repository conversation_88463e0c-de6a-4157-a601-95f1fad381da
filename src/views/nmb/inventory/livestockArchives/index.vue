<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <!-- <el-button type="success" size="mini" icon="el-icon-arrow-left" @click="goback" class="mb10">返回</el-button> -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="140px">
        <el-row class="form_row">
          <el-col class="form_col">
            <el-form-item label="活畜类别：" prop="typeId">
              <el-select v-model="queryParams.typeId" clearable filterable>
                <el-option label="全部" value />
                <el-option
                  v-for="(item, index) in animalsCategory"
                  :label="item.livestockName"
                  :value="item.livestockId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="活畜品种：" prop="varietiesId">
              <el-select v-model="queryParams.varietiesId" clearable filterable>
                <el-option label="全部" value />
                <el-option
                  v-for="(item, index) in animalsVarieties"
                  :label="item.varietiesName"
                  :value="item.varietiesId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="活畜类型：" prop="categoryId">
              <el-select v-model="queryParams.categoryId" clearable filterable>
                <el-option label="全部" value />
                <el-option
                  v-for="(item, index) in animalsType"
                  :label="item.categoryName"
                  :value="item.categoryId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="耳标编号：" prop="earTagNo">
              <el-input v-model="queryParams.earTagNo" placeholder="请输入耳标编号" clearable />
            </el-form-item>
            <el-form-item label="活畜状态：" prop="status">
              <el-select v-model="queryParams.status" clearable filterable>
                <el-option label="全部" value />
                <el-option label="代养" value="3" />
                <el-option label="抵押" value="2" />
                <el-option label="正常" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-left: 140px;">
          <el-col >
            <el-form-item >
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>

            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card shadow="never" class="list_table">
        <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="openBindingLivestockEarTag"
          >绑定耳标</el-button>
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="openBindingLivestock"
          >新增活畜</el-button>
          <!-- 老的绑定活畜暂时不开放了使用上面两个新的-->
          <!-- <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addFrom">绑定活畜</el-button>-->
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <el-table
        ref="multipleTable"
        :header-cell-class-name="cellClass"
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :height="tableHeight"
        border
      >
        <!-- <el-table-column type="selection" width="55" :selectable="selectEnable"></el-table-column> -->
        <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
        <el-table-column align="center" prop="earTagNo" label="耳标编号" min-width="130"/>
        <el-table-column align="center" prop="varietiesName" label="活畜品种"></el-table-column>
        <el-table-column align="center" prop="categoryName" label="活畜类型"></el-table-column>
         <el-table-column align="center" prop="livestockAge" label="月龄">
          <template slot-scope="scope">{{ handelAge(scope.row.livestockAge, ageData) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="livestockWeight" label="体重（Kg）" width="100"></el-table-column>
        <el-table-column align="center" prop="pastureName" label="养殖场"></el-table-column>
        <el-table-column align="center" prop="penName" label="所在圈舍"></el-table-column>
        <el-table-column align="center" prop="birthday" label="出生日期"></el-table-column>
        <el-table-column align="center" prop="birthday" label="入栏日期"></el-table-column>
        <el-table-column align="center" prop="healthStatus" label="健康状况" min-width="90">
          <template
            slot-scope="scope"
          >
          <el-tag :type="formartStatus(scope.row.healthStatus)">
            {{ getLivestockHealthStatusVal(scope.row.healthStatus) }}
          </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="240" align="center">
          <template slot-scope="scope">
            <el-button
              @click="handelEdit(scope.row)"
              icon="el-icon-edit"
              type="text"
              class="btn_color_t"
              v-show="scope.row.earTagNo && !isShopChamberlain"
            >编辑</el-button>
           <!--  <el-button
              @click="unBinding(scope.row)"
              icon="el-icon-delete"
              type="text"
              class="btn_color_f"
              v-if="scope.row.status == 1 && scope.row.earTagNo && !isShopChamberlain"
            >解绑</el-button> -->
            <el-button
              @click="getDetail(scope.row)"
              icon="el-icon-view"
              type="text"
            >查看档案</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <bindingEarTagModel
      v-if="dialogEar.open"
      :dialogEar="dialogEar"
      @close="close"
      @refresh="refreshList"
      ref="tagModel"
    ></bindingEarTagModel>
    <batchImage v-if="batch.open" :batch="batch" @close="close" @refresh="refreshList"></batchImage>
    <livestockDetails v-if="dialog.open" :dialog="dialog" :livestockInfo="livestockInfo"></livestockDetails>
    <binding-livestock v-if="dialog.openBindLivestock" :dialog="dialog" @close="closeBindingLivestock"
      @refresh="refreshList" ref="bindingLivestockRef"></binding-livestock>
    <binding-livestock-ear-tag v-if="dialog.openBingEarTag" :dialog="dialog" @close="closeBindingLivestockEarTag"
      @refresh="refreshList" ref="bindingLivestockEarTagRef"></binding-livestock-ear-tag>
  </div>
</template>
<script>
// import batchImage from "./components/batchImage.vue";
import {
  earTagDelete,
  exportList,
  unboundLivestock,
  batchUnboundLivestock,
} from "@/api/ffs/supervisionSheet/siteManagement.js";
import {
  pastureLivList,
  livestockList,
  animalTypeList,
  varietiesList
} from '@/api/nmb/inventory/index.js'
import bindingEarTagModel from "./components/bindingEarTagModel.vue";
import { getDicts } from "@/api/system/dict/data.js";
import { exportExcel } from "@/utils/east";
import { tableUi } from "@/utils/mixin/tableUi.js";
import livestockDetails from '@/views/xmb/livestock/livestockDetails'
import BindingLivestock from "./components/bindingLivestock";
import BindingLivestockEarTag from "./components/bindingLivestockEarTag";
import { type } from "os";
export default {
    mixins: [tableUi],
  components: {
    BindingLivestockEarTag,
    BindingLivestock,
    bindingEarTagModel,
    // batchImage,
    livestockDetails
  },
  data() {
    return {
      dialog: {
        open: false,
        title: "",
        openBindLivestock: false,
        openBingEarTag: false
      },
      dialogEar: {
        open: false,
        title: "",
        id: "",
        tab: false,
      },
      batch: {
        open: false,
        title: "",
      },
      livestockInfo: {},
      // 全选按钮隐藏
      DisableSelection: true,
      animalsCategory: [], //活畜类别
      animalsVarieties: [], //品种
      animalsType: [], //类型
      srcList: [""],
      ids: [],
      queryParams: {
        status: "",
        earTagNo: "",
        categoryId: "",
        varietiesId: "",
        livestockId: "",
        pastureId: "",
        typeId: "",
        pageNum: 1,
        pageSize: 10,
      },
      loading: true,
      total: 0,
      tableData: [],
      ageData: [], //年龄
      earRagData: [], //编号类型
      isShopChamberlain: 0
    };
  },
  computed: {
    handelImage() {
      return (url) => {
        if (!url) {
          return false;
        }
        if (url.indexOf("[") == 0) {
          console.log("0000", JSON.parse(url)[0].url);
          return JSON.parse(url)[0].url;
        } else {
          return url.split(",")[0];
        }
      };
    },
    handelAge(value, list) {
      return (value, list) => {
        let name = "";
        list.forEach((item) => {
          if (value == item.dictValue) {
            name = item.dictLabel;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getEarTagData();
    this.getAgeData();
    this.queryParams.pastureId = this.$route.query.pastureId;
    this.getAdminShopList();
    this.getCategory();
    this.getVarieties();
    this.getType();
  },

  methods: {
    selectEnable(row) {
      if (row.status == 1 && row.earTagNo) {
        this.DisableSelection = false;
        return true;
      } else {
        return false;
      }
    },
    getLivestockStatusVal(status){
      if(status === 1){
        return "正常";
      }else if(status === 2){
        return "抵押"
      }else if(status === 3){
        return "代养"
      }
      return "--";
    },
    getLivestockHealthStatusVal(status){
      if(status === 1){
        return "健康";
      }else if(status === 2){
        return "需观察"
      }else if(status === 3){
        return "需治疗"
      }else if(status === 4){
        return "死亡"
      }
      return "--";
    },
    formartStatus(status){
      if(status == 1){
        return "type";
      }else if(status == 2){
        return "warning"
      }else if(status == 3){
        return "danger"
      }else if(status == 4){
        return "danger"
      }
    },
    getAdminShopList() {
      // adminShopList({}).then(res => {
      //   this.isShopChamberlain = res.result.isShopChamberlain
      //   this.getList()
      // })
      this.getList()
    },
    //全选按钮隐藏
    cellClass(row) {
      if (this.DisableSelection) {
        // if (row.columnIndex === 0) {
        return "DisableSelection";
        // }
      }
    },
    //多选
    handleSelectionChange(val) {
      this.ids = [];
      val.forEach((item) => {
        this.ids.push({
          livestockId: item.livestockId,
          earTagNo: item.earTagNo,
        });
      });
    },
    //批量解绑
    batchDel() {
      if (this.ids.length <= 0) {
        this.$message.error("请选择活畜");
        return;
      }
      batchUnboundLivestock(this.ids).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "解绑成功",
          });
          this.getList();
          this.ids = [];
        }
      });
    },
    //查看数据字典
    getAgeData() {
      getDicts("livestock_age").then((res) => {
        if (res.code == 200) {
          this.ageData = res.data;
        }
      });
    },
    getEarTagData() {
      getDicts("livestock_code_type").then((res) => {
        if (res.code == 200) {
          this.earRagData = res.data;
        }
      });
    },
    //查看大图
    bigImage(url) {
      if (!url) {
        return;
      }
      this.srcList = [];
      this.srcList.push(url);
    },
    //列表查询
    getList() {
      pastureLivList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res?.result?.list || [];
          this.total = Number(res?.result?.total);
          this.loading = false;
        }
      });
    },
    //获取活畜信息
    getCategory() {
      livestockList({ pageNum: 1, pageSize: 100000 }).then((res) => {
        this.animalsCategory = res.result;
      });
    },

    getVarieties() {
      animalTypeList({ pageNum: 1, pageSize: 100000 }).then((res) => {
        this.animalsType = res.result;
      });
    },
    //或者品种
    getType() {
      varietiesList({ pageNum: 1, pageSize: 100000 }).then((res) => {
        this.animalsVarieties = res.result;
      });
    },
    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialogEar.open = false;
      this.dialogEar.id = "";
      this.batch.open = false;
    },
    closeBindingLivestock(){
      this.dialog.openBindLivestock = false;
    },
    closeBindingLivestockEarTag(){
      this.dialog.openBingEarTag = false;
    },
    openBindingLivestock(){
      this.dialog.openBindLivestock = true;
    },
    openBindingLivestockEarTag(){
      this.dialog.openBingEarTag = true;
    },
    //重置
    resetQuery() {
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addFrom() {
      this.dialogEar.open = true;
      this.dialogEar.tab = false;
    },
    //编辑数据
    handelEdit(row) {
      this.dialogEar.tab = true;
      this.dialogEar.open = true;
      if (row.livestockImage && row.livestockImage.indexOf("[") == 0) {
        let url = [];
        JSON.parse(row.livestockImage).forEach((item) => {
          url.push(item.url);
        });
        row.livestockImage = url.toString();
      }
      let rowData = JSON.parse(JSON.stringify(row));
      this.$nextTick(() => {
        //   if (row.source == 1&& rowData.earTagNo.indexOf('A')==0) {
        //     rowData.earTagNo = rowData.earTagNo.slice(1);
        //   } else {
        //     rowData.earTagNo = rowData.earTagNo;
        //   }
        this.$refs.tagModel.form = rowData;
        this.$refs.tagModel.prefix = "";
        if (row.status == 2) {
          this.$refs.tagModel.disable = true;
        } else {
          this.$refs.tagModel.disable = false;
        }
      });
    },
    //导出活畜信息
    exportEar() {
      let obj = {
        status: this.queryParams.status,
        earTagNo: this.queryParams.earTagNo,
        categoryId: this.queryParams.categoryId,
        varietiesId: this.queryParams.varietiesId,
        livestockId: this.queryParams.livestockId,
        pastureId: this.queryParams.pastureId,
        typeId: this.queryParams.typeId,
      };
      const filename = "活畜信息";
      exportExcel(exportList, obj, filename);
    },

    //批量上传图片
    batchUp() {
      this.batch.open = true;
      this.batch.title = "批量上传活畜图片";
      this.batch.type = 1;
    },
    //完善活畜信息
    perfectInfo() {
      this.batch.open = true;
      this.batch.title = "完善活畜信息";
      this.batch.type = 2;
    },

    unBinding(row) {
      unboundLivestock({
        livestockId: row.livestockId,
        earTagNo: row.earTagNo,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "解绑成功",
          });
          this.getList();
        }
      });
    },
    //删除活畜
    handelDelete(id) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          earTagDelete({ ids: [id] }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    getDetail(item) {
      this.dialog.livestockId = item.livestockId
      this.livestockId = item.livestockId
      this.earTagNo = item.earTagNo
      this.categoryName = item.categoryName
      this.dialog.open = true;
      this.dialog.source= false
      this.livestockInfo = item
      if(this.livestockInfo?.livestockImage){
        let convertedImg = this.convertToHTTPSURL(this.livestockInfo?.livestockImage);
        this.livestockImage = convertedImg.split(',')
        console.log(this.livestockImage)
      }
    },
  },
};
</script>
<style lang="scss" >
.el-table .DisableSelection .el-checkbox {
  display: none;
}
</style>
