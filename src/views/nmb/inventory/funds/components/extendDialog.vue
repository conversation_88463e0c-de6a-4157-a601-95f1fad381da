<template>
  <el-dialog title="延长监管日期" :visible.sync="visible" width="500px" :close-on-click-modal="false" @close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
     <!--  <el-form-item label="采购订单编号">
        <span>{{ orderData.purchaseOrderCode }}</span>
      </el-form-item>
      
      <el-form-item label="监管方公司">
        <span>{{ orderData.companyName }}</span>
      </el-form-item>
      
      <el-form-item label="当前到期日期">
        <span>{{ orderData.supervisionEndDate }}</span>
      </el-form-item>
       -->
      <el-form-item label="结束监管日期" prop="supervisionEndDate" required>
        <el-date-picker 
          v-model="form.supervisionEndDate" 
          type="date" 
          placeholder="请选择结束监管日期" 
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
          <!-- :picker-options="pickerOptions" -->
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateSupervisionEndDate } from '@/api/nmb/inventory/index.js'

export default {
  name: 'ExtendDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        supervisionEndDate: ''
      },
      rules: {
        supervisionEndDate: [
          { required: true, message: '请选择结束监管日期', trigger: 'change' },
          // { validator: this.validateEndDate, trigger: 'change' }
        ]
      },
      submitLoading: false,
      pickerOptions: {
        disabledDate: (time) => {
          // 禁用当前到期日期之前的日期
          if (this.orderData.supervisionEndDate) {
            return time.getTime() <= new Date(this.orderData.supervisionEndDate).getTime()
          }
          return time.getTime() < Date.now() - 8.64e7 
        }
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
      }
    }
  },
  methods: {
    validateEndDate(rule, value, callback) {
      if (value && this.orderData.supervisionEndDate) {
        if (new Date(value) <= new Date(this.orderData.supervisionEndDate)) {
          callback(new Error('新的到期日期必须晚于当前到期日期'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
          
          const params = {
            purchaseSuperviseId: this.orderData.purchaseSuperviseId,
            supervisionEndDate: this.form.supervisionEndDate
          }
          updateSupervisionEndDate(params).then(res => {
            if (res.code === 200) {
              this.$message.success('延长监管日期成功')
              this.$emit('success')
              this.handleClose()
            } else {
              this.$message.error(res.message || '延长监管日期失败')
            }
            this.submitLoading = false
          })
        }
      })
    },
    
    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        supervisionEndDate: ''
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>
