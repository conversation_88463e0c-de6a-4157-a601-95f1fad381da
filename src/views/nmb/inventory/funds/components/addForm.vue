<template>
  <el-dialog title="新增资金监管" :visible.sync="visible" width="600px" :close-on-click-modal="false" @close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="采购订单" prop="purchaseOrderId" required>
        <div class="order-input-wrapper" @click="showOrderDialog">
          <el-input
            ref="orderInput"
            v-model="selectedOrderCode"
            placeholder="弹窗（已备货、带待发车的采购订单）"
            readonly
            style="pointer-events: none;"
          />
        </div>
      </el-form-item>
      
      <el-form-item label="监管方公司名称" prop="companyName" required>
        <el-input v-model="form.companyName" placeholder="请选择" />
      </el-form-item>
      
      <el-form-item label="资金金额（万元）" prop="capitalAmount" required>
        <el-input v-model.number="form.capitalAmount" placeholder="请输入" type="number" min="0" step="0.01" />
      </el-form-item>
      
      <el-form-item label="资金到账日期" prop="fundsArrivalDate" required>
        <el-date-picker 
          v-model="form.fundsArrivalDate" 
          type="date" 
          placeholder="请选择" 
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="开始监管日期" prop="supervisionStartDate" required>
        <el-input v-model="supervisionStartDateDisplay" placeholder="默认资金到账日期，不可修改" readonly />
      </el-form-item>
      
      <el-form-item label="结束监管日期" prop="supervisionEndDate" required>
        <el-date-picker 
          v-model="form.supervisionEndDate" 
          type="date" 
          placeholder="请选择" 
          value-format="yyyy-MM-dd"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
    </div>

    <el-dialog title="选择采购订单" :visible.sync="orderDialogVisible" width="1200px" append-to-body>
      <el-table 
        :data="orderList" 
        border 
        v-loading="orderLoading" 
        @selection-change="handleOrderSelection"
        max-height="400"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="purchaseOrderCode" label="订单编号" align="center" min-width="180" />
        <el-table-column prop="projectManagerName" label="项目经理" align="center" min-width="100" />
        <el-table-column prop="provinceName" label="牛源地" align="center" min-width="80" />
        <el-table-column prop="brokerName" label="牛经纪" align="center" min-width="100" />
        <el-table-column prop="driverName" label="运输司机" align="center" min-width="100" />
        <el-table-column prop="licensePlateNumber" label="运输车牌号" align="center" min-width="120" />
        <el-table-column prop="livestockNum" label="计划采购数量" align="center" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.livestockNum }}头
          </template>
        </el-table-column>
        <el-table-column prop="deliveryStartTime" label="计划发车日期" align="center" min-width="180" />
        <el-table-column prop="showStatusName" label="状态" align="center" min-width="80" />
      </el-table>
      
      <pagination 
        v-show="orderTotal > 0" 
        :total="orderTotal" 
        :page.sync="orderQueryParams.pageNum" 
        :limit.sync="orderQueryParams.pageSize"
        @pagination="getOrderList" 
      />
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmOrderSelection">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { purchaseSuperviseAdd, purchaseOrderPage } from '@/api/nmb/inventory/index.js'

export default {
  name: 'AddForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        purchaseOrderId: null,
        companyName: '',
        capitalAmount: null,
        fundsArrivalDate: '',
        supervisionStartDate: '',
        supervisionEndDate: ''
      },
      rules: {
        purchaseOrderId: [
          { required: true, message: '请选择采购订单', trigger: 'blur' },
          { validator: this.validatePurchaseOrder, trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入监管方公司名称', trigger: 'blur' }
        ],
        capitalAmount: [
          { required: true, message: '请输入资金金额', trigger: 'blur' },
          { type: 'number', message: '资金金额必须为数字', trigger: 'blur' }
        ],
        fundsArrivalDate: [
          { required: true, message: '请选择资金到账日期', trigger: 'change' }
        ],
        supervisionEndDate: [
          { required: true, message: '请选择结束监管日期', trigger: 'change' },
          { validator: this.validateEndDate, trigger: 'change' }
        ]
      },
      submitLoading: false,
      selectedOrderCode: '', // 选中的订单编号
      
      // 选择订单
      orderDialogVisible: false,
      orderLoading: false,
      orderList: [],
      orderTotal: 0,
      orderQueryParams: {
        pageNum: 1,
        pageSize: 10,
        showStatus: 20
      },
      selectedOrders: [] // 选中的订单
    }
  },
  computed: {
    supervisionStartDateDisplay() {
      return this.form.fundsArrivalDate || '默认资金到账日期，不可修改'
    }
  },
  watch: {
    // 资金到账日期变化，自动设置开始监管日期
    'form.fundsArrivalDate'(newVal) {
      this.form.supervisionStartDate = newVal
    },
    
    visible(val) {
      if (val) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 采购订单
    validatePurchaseOrder(rule, value, callback) {
      if (!value) {
        callback(new Error('请选择采购订单'))
      } else {
        callback()
      }
    },

    validateEndDate(rule, value, callback) {
      if (value && this.form.supervisionStartDate) {
        if (new Date(value) <= new Date(this.form.supervisionStartDate)) {
          callback(new Error('结束监管日期必须晚于开始监管日期'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    showOrderDialog() {
      console.log('showOrderDialog called - method executed!')
      this.orderDialogVisible = true
      this.getOrderList()
    },
    
    getOrderList() {
      this.orderLoading = true
      purchaseOrderPage(this.orderQueryParams).then(res => {
        if (res.code === 200) {
          this.orderList = res.result.list || []
          this.orderTotal = Number(res.result.total || 0)
        } else {
          this.$message.error(res.message || '获取订单列表失败')
        }
        this.orderLoading = false
      }).catch(error => {
        console.error('获取订单列表失败:', error)
        this.$message.error('获取订单列表失败')
        this.orderLoading = false
      })
    },
    
    handleOrderSelection(selection) {
      this.selectedOrders = selection
    },
    
    confirmOrderSelection() {
      if (this.selectedOrders.length === 0) {
        this.$message.warning('请选择一个订单')
        return
      }
      if (this.selectedOrders.length > 1) {
        this.$message.warning('只能选择一个订单')
        return
      }

      const selectedOrder = this.selectedOrders[0]
      this.form.purchaseOrderId = selectedOrder.purchaseOrderId
      this.selectedOrderCode = selectedOrder.purchaseOrderCode
      this.orderDialogVisible = false

      this.$nextTick(() => {
        this.$refs.form.validateField('purchaseOrderId')
      })
    },
    
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
          
          const params = {
            purchaseOrderId: this.form.purchaseOrderId,
            companyName: this.form.companyName,
            capitalAmount: this.form.capitalAmount * 10000, 
            fundsArrivalDate: this.form.fundsArrivalDate,
            supervisionStartDate: this.form.supervisionStartDate,
            supervisionEndDate: this.form.supervisionEndDate
          }
          console.log(params)
          purchaseSuperviseAdd(params).then(res => {
            if (res.code === 200) {
              this.$message.success('操作成功')
              this.$emit('success')
              this.handleClose()
            } else {
              this.$message.error(res.message || '操作失败')
            }
            this.submitLoading = false
          })
        }
      })
    },
    
    handleClose() {
      this.$emit('update:visible', false)
    },
    
    resetForm() {
      this.form = {
        purchaseOrderId: null,
        companyName: '',
        capitalAmount: null,
        fundsArrivalDate: '',
        supervisionStartDate: '',
        supervisionEndDate: ''
      }
      this.selectedOrderCode = ''
      this.selectedOrders = []
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}

.order-input-wrapper {
  cursor: pointer;

  .el-input {
    ::v-deep .el-input__inner {
      cursor: pointer;
      background-color: #fff;
    }
  }
}

.el-input[readonly] {
  cursor: pointer;

  ::v-deep .el-input__inner {
    cursor: pointer;
    background-color: #fff;
  }
}
</style>
