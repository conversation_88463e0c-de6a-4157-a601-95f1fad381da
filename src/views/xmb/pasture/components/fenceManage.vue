<template>
  <div>
    <el-dialog
      :title="dialogFence.title"
      :visible.sync="dialogFence.open"
      width="800px"
      :close-on-click-modal="false"
      @close="close"
      class="fenceManageDialog"
      append-to-body
    >
      <!-- 栏位列表 -->
      <div class="fence-list-section">
        <div class="section-header">
          <h4>当前栏位列表</h4>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="addNewFence">新增栏位</el-button>
        </div>
        
        <el-table :data="fenceList" stripe style="width: 100%" v-loading="loading" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="fenceCode" label="栏位编号" align="center" />
          <el-table-column label="栏位照片" align="center" width="120">
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.fenceUrl"
                style="width: 60px; height: 40px"
                :src="scope.row.fenceUrl"
                :preview-src-list="[scope.row.fenceUrl]"
                fit="cover"
              ></el-image>
              <span v-else>暂无图片</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="120" align="center">
            <template slot-scope="scope">
              <el-button class="btn_color_t" @click="editFence(scope.row)" icon="el-icon-edit" size="mini" type="text">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑栏位表单区域 -->
      <div class="fence-form-section" v-if="showForms">
        <div class="section-header">
          <h4>{{ isEdit ? '编辑栏位' : '新增栏位' }}</h4>
        </div>
        
        <div class="fence-forms">
          <div 
            v-for="(form, index) in fenceForms" 
            :key="form.id"
            class="fence-form-item"
          >
            <el-card shadow="never" class="form-card">
              <div slot="header" class="form-card-header">
                <span>栏位 {{ index + 1 }}</span>
                <el-button 
                  v-if="!isEdit && fenceForms.length > 1" 
                  type="text" 
                  icon="el-icon-delete" 
                  @click="removeFenceForm(index)"
                  class="remove-btn"
                >删除</el-button>
              </div>
              
              <el-form
                :model="form"
                :rules="fenceRules"
                :ref="`fenceForm${index}`"
                label-width="100px"
                class="fence-form"
              >
                <el-form-item label="栏位编号" prop="fenceCode">
                  <el-input v-model="form.fenceCode" placeholder="请输入栏位编号" />
                </el-form-item>
                <el-form-item label="栏位照片" prop="fenceUrl">
                  <el-input v-model="form.fenceUrl" placeholder="请输入栏位照片URL" />
                </el-form-item>
              </el-form>
              
              <div class="form-actions">
                <el-button @click="cancelFenceForm(index)">取消</el-button>
                <el-button type="primary" @click="confirmFenceForm(index)">确认</el-button>
              </div>
            </el-card>
          </div>
        </div>
        
        <div class="add-more-section" v-if="!isEdit">
          <el-button type="text" icon="el-icon-plus" @click="addMoreFence" class="add-more-btn">添加更多栏位</el-button>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { fenceList, fenceAdd, fenceEdit } from "@/api/ffs/supervisionSheet/siteManagement";

export default {
  props: {
    dialogFence: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      fenceList: [],
      showForms: false,
      isEdit: false,
      fenceForms: [],
      nextFormId: 1,
      fenceRules: {
        fenceCode: [
          {
            required: true,
            message: "请填写栏位编号",
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    'dialogFence.open'(newVal) {
      if (newVal) {
        this.getFenceList();
        this.resetForms();
      }
    }
  },
  methods: {
    // 获取栏位列表
    getFenceList() {
      if (!this.dialogFence.penId) return;
      
      this.loading = true;
      fenceList({ pid: this.dialogFence.penId }).then((res) => {
        if (res.code === 200) {
          this.fenceList = res.result || [];
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 新增栏位
    addNewFence() {
      this.showForms = true;
      this.isEdit = false;
      this.fenceForms = [this.createEmptyForm()];
    },
    
    // 编辑栏位
    editFence(fence) {
      this.showForms = true;
      this.isEdit = true;
      this.fenceForms = [{
        id: this.nextFormId++,
        penId: fence.penId,
        fenceCode: fence.fenceCode,
        fenceUrl: fence.fenceUrl || ''
      }];
    },
    
    // 创建空表单
    createEmptyForm() {
      return {
        id: this.nextFormId++,
        penId: this.dialogFence.penId,
        fenceCode: '',
        fenceUrl: ''
      };
    },
    
    // 添加更多栏位表单
    addMoreFence() {
      this.fenceForms.push(this.createEmptyForm());
    },
    
    // 删除栏位表单
    removeFenceForm(index) {
      this.fenceForms.splice(index, 1);
      if (this.fenceForms.length === 0) {
        this.showForms = false;
      }
    },
    
    // 取消栏位表单
    cancelFenceForm(index) {
      if (this.isEdit) {
        this.showForms = false;
        this.resetForms();
      } else {
        this.removeFenceForm(index);
      }
    },
    
    // 确认栏位表单
    confirmFenceForm(index) {
      const formRef = this.$refs[`fenceForm${index}`];
      if (!formRef || !formRef[0]) return;
      
      formRef[0].validate((valid) => {
        if (!valid) return;
        
        const form = this.fenceForms[index];
        
        if (this.isEdit) {
          this.updateFence(form, index);
        } else {
          this.saveFence(form, index);
        }
      });
    },
    
    // 保存新增栏位
    saveFence(form, index) {
      fenceAdd(form).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "栏位添加成功",
          });
          this.removeFenceForm(index);
          this.getFenceList();
          this.$emit("refresh");
        }
      });
    },
    
    // 更新栏位
    updateFence(form, index) {
      fenceEdit(form).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "栏位编辑成功",
          });
          this.showForms = false;
          this.resetForms();
          this.getFenceList();
          this.$emit("refresh");
        }
      });
    },
    
    // 重置表单
    resetForms() {
      this.showForms = false;
      this.isEdit = false;
      this.fenceForms = [];
      this.nextFormId = 1;
    },
    
    // 关闭弹窗
    close() {
      this.resetForms();
      this.$emit("close");
    }
  }
};
</script>

<style lang="scss" scoped>
.fenceManageDialog {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
}

.fence-list-section, .fence-form-section {
  margin-bottom: 20px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
    }
  }
}

.fence-forms {
  .fence-form-item {
    margin-bottom: 15px;
    
    .form-card {
      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .remove-btn {
          color: #f56c6c;
        }
      }
      
      .fence-form {
        margin-bottom: 15px;
      }
      
      .form-actions {
        text-align: right;
        border-top: 1px solid #ebeef5;
        padding-top: 15px;
      }
    }
  }
}

.add-more-section {
  text-align: center;
  padding: 20px 0;
  border-top: 1px dashed #dcdfe6;
  
  .add-more-btn {
    font-size: 14px;
    color: #409eff;
  }
}
</style>
