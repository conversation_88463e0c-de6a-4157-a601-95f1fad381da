<template>
  <div>
    <el-dialog
      :title="dialogFence.title"
      :visible.sync="dialogFence.open"
      width="800px"
      :close-on-click-modal="false"
      @close="close"
      class="fenceManageDialog"
      append-to-body
    >
      <!-- 栏位列表 -->
      <div class="fence-list-section">
        <div class="section-header">
          <h4>当前栏位列表</h4>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="addNewFence">新增栏位</el-button>
        </div>
        
        <el-table :data="fenceList" stripe style="width: 100%" v-loading="loading" border>
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="fenceCode" label="栏位编号" align="center" />
          <el-table-column label="栏位照片" align="center" width="120">
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.fenceUrl"
                style="width: 60px; height: 40px"
                :src="scope.row.fenceUrl"
                :preview-src-list="[scope.row.fenceUrl]"
                fit="cover"
              ></el-image>
              <span v-else>暂无图片</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="120" align="center">
            <template slot-scope="scope">
              <el-button class="btn_color_t" @click="editFence(scope.row)" icon="el-icon-edit" size="mini" type="text">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑栏位表单区域 -->
      <div class="fence-form-section" v-if="showForm">
        <div class="section-header">
          <h4>{{ isEdit ? '编辑栏位' : '新增栏位' }}</h4>
        </div>

        <el-card shadow="never" class="form-card">
          <el-form
            :model="fenceForm"
            :rules="fenceRules"
            ref="fenceFormRef"
            label-width="100px"
            class="fence-form"
          >
            <el-form-item label="栏位编号" prop="fenceCode">
              <el-input v-model="fenceForm.fenceCode" placeholder="请输入栏位编号" />
            </el-form-item>
            <el-form-item label="栏位照片" prop="fenceUrl">
              <image-upload
                v-model="fenceForm.fenceUrl"
                :limit="1"
                :fileSize="2"
                :isShowTip="true"
              ></image-upload>
            </el-form-item>
          </el-form>

          <div class="form-actions">
            <el-button @click="cancelForm">取消</el-button>
            <el-button type="primary" @click="confirmForm" :loading="submitLoading">确认</el-button>
          </div>
        </el-card>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { fenceList, fenceAdd, fenceEdit } from "@/api/ffs/supervisionSheet/siteManagement";

export default {
  props: {
    dialogFence: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      fenceList: [],
      showForm: false,
      isEdit: false,
      submitLoading: false,
      fenceForm: {
        fenceCode: '',
        fenceUrl: '',
        penId: null
      },
      fenceRules: {
        fenceCode: [
          {
            required: true,
            message: "请填写栏位编号",
            trigger: "blur",
          },
        ],
        fenceUrl: [
          {
            required: true,
            message: "请上传栏位照片",
            trigger: "change",
          },
        ],
      },
    };
  },
  watch: {
    'dialogFence.open'(newVal) {
      if (newVal) {
        this.getFenceList();
        this.resetForm();
      }
    }
  },
  methods: {
    // 获取栏位列表
    getFenceList() {
      if (!this.dialogFence.penId) return;
      
      this.loading = true;
      fenceList({ pid: this.dialogFence.penId }).then((res) => {
        if (res.code === 200) {
          this.fenceList = res.result || [];
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 新增栏位
    addNewFence() {
      // 如果当前有表单显示，不允许创建新的表单
      if (this.showForm) {
        this.$message.warning('请先完成当前表单的提交或取消操作');
        return;
      }
      this.showForm = true;
      this.isEdit = false;
      this.resetFormData();
    },

    // 编辑栏位
    editFence(fence) {
      // 如果当前有表单显示，不允许编辑
      if (this.showForm) {
        this.$message.warning('请先完成当前表单的提交或取消操作');
        return;
      }
      this.showForm = true;
      this.isEdit = true;
      this.fenceForm = {
        penId: fence.penId, // 栏位ID，用于编辑
        fenceCode: fence.fenceCode,
        fenceUrl: fence.fenceUrl || ''
      };
    },

    // 重置表单数据
    resetFormData() {
      this.fenceForm = {
        fenceCode: '',
        fenceUrl: '',
        penId: this.dialogFence.penId // 圈舍ID，用于新增
      };
    },
    
    
    // 取消表单
    cancelForm() {
      this.showForm = false;
      this.resetForm();
    },

    // 确认表单
    confirmForm() {
      this.$refs.fenceFormRef.validate((valid) => {
        if (!valid) return;

        this.submitLoading = true;

        if (this.isEdit) {
          this.updateFence();
        } else {
          this.saveFence();
        }
      });
    },
    
    // 保存新增栏位
    saveFence() {
      // 处理图片URL格式
      let fenceUrl = this.fenceForm.fenceUrl;
      if (Array.isArray(fenceUrl)) {
        fenceUrl = fenceUrl.length > 0 ? fenceUrl[0] : '';
      }

      // 只传递需要的字段
      const params = {
        fenceCode: this.fenceForm.fenceCode,
        fenceUrl: fenceUrl,
        penId: this.fenceForm.penId
      };

      fenceAdd(params).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "栏位添加成功",
          });
          this.showForm = false;
          this.resetForm();
          this.getFenceList();
          this.$emit("refresh");
        }
        this.submitLoading = false;
      }).catch(() => {
        this.submitLoading = false;
      });
    },

    // 更新栏位
    updateFence() {
      // 处理图片URL格式
      let fenceUrl = this.fenceForm.fenceUrl;
      if (Array.isArray(fenceUrl)) {
        fenceUrl = fenceUrl.length > 0 ? fenceUrl[0] : '';
      }

      // 只传递需要的字段
      const params = {
        fenceCode: this.fenceForm.fenceCode,
        fenceUrl: fenceUrl,
        penId: this.fenceForm.penId
      };

      fenceEdit(params).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "栏位编辑成功",
          });
          this.showForm = false;
          this.resetForm();
          this.getFenceList();
          this.$emit("refresh");
        }
        this.submitLoading = false;
      }).catch(() => {
        this.submitLoading = false;
      });
    },
    


    // 重置表单
    resetForm() {
      this.showForm = false;
      this.isEdit = false;
      this.submitLoading = false;
      this.resetFormData();
      if (this.$refs.fenceFormRef) {
        this.$refs.fenceFormRef.clearValidate();
      }
    },

    // 关闭弹窗
    close() {
      this.resetForm();
      this.$emit("close");
    }
  }
};
</script>

<style lang="scss" scoped>
.fenceManageDialog {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
}

.fence-list-section, .fence-form-section {
  margin-bottom: 20px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
    }
  }
}

.form-card {
  .fence-form {
    margin-bottom: 15px;
  }

  .form-actions {
    text-align: right;
    border-top: 1px solid #ebeef5;
    padding-top: 15px;
  }
}


</style>
